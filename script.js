// Mobile Navigation Toggle
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');

navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
const navLinks = document.querySelectorAll('.nav-link');
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
    });
});

// Smooth scrolling for navigation links
navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);
        
        if (targetSection) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetSection.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Header background change on scroll
const header = document.querySelector('.header');
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        header.style.backgroundColor = 'rgba(0, 0, 0, 0.98)';
    } else {
        header.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
    }
});

// Intersection Observer for fade-in animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('fade-in');
        }
    });
}, observerOptions);

// Observe elements for animation
const animateElements = document.querySelectorAll('.game-card, .news-card, .stat, .about-text');
animateElements.forEach(el => {
    observer.observe(el);
});

// Hero buttons functionality
const exploreGamesBtn = document.querySelector('.btn-primary');
const learnMoreBtn = document.querySelector('.btn-secondary');

exploreGamesBtn.addEventListener('click', () => {
    document.querySelector('#games').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
});

learnMoreBtn.addEventListener('click', () => {
    document.querySelector('#about').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
});

// Game cards hover effect enhancement
const gameCards = document.querySelectorAll('.game-card');
gameCards.forEach(card => {
    card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-8px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)';
    });
});

// News cards click functionality
const newsCards = document.querySelectorAll('.news-card');
newsCards.forEach(card => {
    card.addEventListener('click', () => {
        // Simulate opening news article
        const title = card.querySelector('h3').textContent;
        alert(`Opening article: "${title}"\n\nThis would normally navigate to the full article page.`);
    });
    
    // Add cursor pointer style
    card.style.cursor = 'pointer';
});

// Stats counter animation
const stats = document.querySelectorAll('.stat h3');
const animateStats = () => {
    stats.forEach(stat => {
        const target = parseInt(stat.textContent.replace(/\D/g, ''));
        const suffix = stat.textContent.replace(/\d/g, '');
        let current = 0;
        const increment = target / 50;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(current) + suffix;
        }, 30);
    });
};

// Trigger stats animation when about section is visible
const aboutSection = document.querySelector('#about');
const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateStats();
            statsObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

statsObserver.observe(aboutSection);

// Social links functionality
const socialLinks = document.querySelectorAll('.social-link');
socialLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const platform = link.querySelector('.material-icons').textContent;
        alert(`This would open our ${platform} page.\n\nSocial media integration would be implemented here.`);
    });
});

// Contact form simulation (if needed later)
const createContactForm = () => {
    const contactSection = document.createElement('section');
    contactSection.id = 'contact';
    contactSection.className = 'contact';
    contactSection.innerHTML = `
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <div class="contact-item">
                        <span class="material-icons">email</span>
                        <p><EMAIL></p>
                    </div>
                    <div class="contact-item">
                        <span class="material-icons">phone</span>
                        <p>+****************</p>
                    </div>
                    <div class="contact-item">
                        <span class="material-icons">location_on</span>
                        <p>123 Game Dev Street, Tech City</p>
                    </div>
                </div>
                <form class="contact-form">
                    <input type="text" placeholder="Your Name" required>
                    <input type="email" placeholder="Your Email" required>
                    <input type="text" placeholder="Subject" required>
                    <textarea placeholder="Your Message" rows="5" required></textarea>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </form>
            </div>
        </div>
    `;
    
    // Insert before footer
    const footer = document.querySelector('.footer');
    footer.parentNode.insertBefore(contactSection, footer);
};

// Keyboard navigation support
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        navMenu.classList.remove('active');
    }
});

// Loading screen (optional)
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    
    // Add stagger animation to hero elements
    const heroElements = document.querySelectorAll('.hero-title, .hero-subtitle, .hero-buttons');
    heroElements.forEach((el, index) => {
        setTimeout(() => {
            el.classList.add('fade-in');
        }, index * 200);
    });
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    const rate = scrolled * -0.5;
    
    if (hero) {
        hero.style.transform = `translateY(${rate}px)`;
    }
});

// Dynamic year in footer
const currentYear = new Date().getFullYear();
const footerYear = document.querySelector('.footer-bottom p');
if (footerYear) {
    footerYear.textContent = `© ${currentYear} Studio Black. All rights reserved.`;
}

// Game card click functionality
gameCards.forEach(card => {
    card.addEventListener('click', () => {
        const gameTitle = card.querySelector('h3').textContent;
        const gameDescription = card.querySelector('p').textContent;
        
        // Create modal or redirect to game page
        showGameModal(gameTitle, gameDescription);
    });
});

// Game modal function
function showGameModal(title, description) {
    const modal = document.createElement('div');
    modal.className = 'game-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>${title}</h2>
            <div class="modal-game-image">
                <div class="placeholder-image">
                    <span class="material-icons">videogame_asset</span>
                </div>
            </div>
            <p>${description}</p>
            <div class="modal-buttons">
                <button class="btn btn-primary">Play Now</button>
                <button class="btn btn-secondary">Learn More</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// Search functionality (if needed)
const addSearchFeature = () => {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <input type="text" id="search-input" placeholder="Search games, news...">
        <span class="material-icons search-icon">search</span>
    `;
    
    const nav = document.querySelector('.nav');
    nav.appendChild(searchContainer);
    
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        // Implement search logic here
        console.log('Searching for:', query);
    });
};

// Newsletter subscription
const addNewsletterSignup = () => {
    const newsletterSection = document.createElement('div');
    newsletterSection.className = 'newsletter-signup';
    newsletterSection.innerHTML = `
        <div class="container">
            <h3>Stay Updated</h3>
            <p>Subscribe to our newsletter for the latest game updates and news.</p>
            <form class="newsletter-form">
                <input type="email" placeholder="Enter your email" required>
                <button type="submit" class="btn btn-primary">Subscribe</button>
            </form>
        </div>
    `;
    
    const footer = document.querySelector('.footer');
    footer.parentNode.insertBefore(newsletterSection, footer);
    
    const newsletterForm = newsletterSection.querySelector('.newsletter-form');
    newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const email = newsletterForm.querySelector('input').value;
        alert(`Thank you for subscribing with email: ${email}`);
        newsletterForm.reset();
    });
};

// Initialize additional features
document.addEventListener('DOMContentLoaded', () => {
    // Uncomment these if you want additional features
    // addNewsletterSignup();
    // addSearchFeature();
    
    console.log('Studio Black website loaded successfully!');
});
