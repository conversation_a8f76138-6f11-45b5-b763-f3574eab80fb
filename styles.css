/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-black: #000000;
    --secondary-black: #1a1a1a;
    --dark-gray: #2d2d2d;
    --medium-gray: #404040;
    --light-gray: #666666;
    --accent-color: #ff6b35;
    --text-white: #ffffff;
    --text-gray: #cccccc;
    --text-light-gray: #999999;
    --shadow: rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--primary-black);
    color: var(--text-white);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand h1 {
    font-family: 'Roboto Slab', serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-white);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    cursor: pointer;
    color: var(--text-white);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 2rem;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.hero-title {
    font-family: 'Roboto Slab', serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-white) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-gray);
    margin-bottom: 2rem;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-white);
    border: 2px solid var(--medium-gray);
}

.btn-secondary:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
    transform: translateY(-2px);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Placeholder Image Styles */
.placeholder-image {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--dark-gray) 0%, var(--medium-gray) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--medium-gray);
}

.placeholder-image .material-icons {
    font-size: 4rem;
    color: var(--text-gray);
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-title {
    font-family: 'Roboto Slab', serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-white);
}

/* About Section */
.about {
    background-color: var(--secondary-black);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    color: var(--text-gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 1.5rem;
    background-color: var(--dark-gray);
    border-radius: 8px;
    border: 1px solid var(--medium-gray);
}

.stat h3 {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-gray);
    font-size: 0.9rem;
}

.about-image .placeholder-image {
    width: 400px;
    height: 300px;
}

/* Games Section */
.games {
    background-color: var(--primary-black);
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.game-card {
    background-color: var(--secondary-black);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--dark-gray);
    transition: var(--transition);
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
    border-color: var(--accent-color);
}

.game-image .placeholder-image {
    width: 100%;
    height: 200px;
    border-radius: 0;
    border: none;
}

.game-info {
    padding: 1.5rem;
}

.game-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--text-white);
}

.game-info p {
    color: var(--text-gray);
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.game-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background-color: var(--dark-gray);
    color: var(--text-gray);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid var(--medium-gray);
}

/* News Section */
.news {
    background-color: var(--secondary-black);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.news-card {
    background-color: var(--dark-gray);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--medium-gray);
    transition: var(--transition);
}

.news-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px var(--shadow);
    border-color: var(--accent-color);
}

.news-card.featured {
    grid-row: span 2;
}

.news-card.featured .news-image .placeholder-image {
    width: 100%;
    height: 250px;
    border-radius: 0;
    border: none;
}

.news-content {
    padding: 1.5rem;
}

.news-date {
    color: var(--accent-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.news-content h3 {
    margin: 0.5rem 0 1rem 0;
    color: var(--text-white);
    font-size: 1.2rem;
}

.news-content p {
    color: var(--text-gray);
    margin-bottom: 1rem;
    font-size: 0.95rem;
    line-height: 1.6;
}

.read-more {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.read-more:hover {
    color: #e55a2b;
}

/* Footer */
.footer {
    background-color: var(--primary-black);
    border-top: 1px solid var(--dark-gray);
    padding: 3rem 0 1rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-white);
    margin-bottom: 1rem;
}

.footer-section h3 {
    color: var(--accent-color);
    font-family: 'Roboto Slab', serif;
}

.footer-section p,
.footer-section li {
    color: var(--text-gray);
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-gray);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--accent-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--dark-gray);
    border-radius: 50%;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--accent-color);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--dark-gray);
    color: var(--text-light-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--primary-black);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: block;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .hero-image .placeholder-image,
    .about-image .placeholder-image {
        width: 250px;
        height: 200px;
    }

    .container {
        padding: 0 15px;
    }

    section {
        padding: 3rem 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .nav {
        padding: 1rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-black);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}
