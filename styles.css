/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-black: #0a0a0a;
    --secondary-black: rgba(30, 30, 30, 0.8);
    --surface-black: rgba(45, 45, 48, 0.6);
    --dark-gray: rgba(60, 64, 67, 0.7);
    --medium-gray: rgba(95, 99, 104, 0.5);
    --light-gray: rgba(128, 134, 139, 0.4);
    --accent-color: #1a73e8;
    --accent-hover: #1557b0;
    --secondary-accent: #34a853;
    --warning-accent: #fbbc04;
    --error-accent: #ea4335;
    --text-white: #ffffff;
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-disabled: #5f6368;
    --divider: rgba(60, 64, 67, 0.3);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-hover: rgba(255, 255, 255, 0.08);
    --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 1px 3px 1px rgba(0, 0, 0, 0.15);
    --shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15);
    --shadow-3: 0 4px 8px 3px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-4: 0 6px 10px 4px rgba(0, 0, 0, 0.15), 0 2px 3px rgba(0, 0, 0, 0.3);
    --shadow-5: 0 8px 12px 6px rgba(0, 0, 0, 0.15), 0 4px 4px rgba(0, 0, 0, 0.3);
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    --transition-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
    --transition-accelerate: cubic-bezier(0.4, 0.0, 1, 1);
    --border-radius-small: 8px;
    --border-radius-medium: 12px;
    --border-radius-large: 16px;
    --border-radius-xl: 24px;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.5;
    overflow-x: hidden;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.25px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(26, 115, 232, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(52, 168, 83, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(251, 188, 4, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles - Transparent Glass Design */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    z-index: 1000;
    transition: all 0.3s var(--transition-standard);
    box-shadow: var(--shadow-glass);
    border-bottom: 1px solid var(--glass-border);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 32px;
    max-width: 1200px;
    margin: 0 auto;
    height: 80px;
}

.nav-brand h1 {
    font-family: 'Google Sans', 'Roboto', sans-serif;
    font-size: 28px;
    font-weight: 500;
    color: var(--accent-color);
    letter-spacing: -0.5px;
    margin: 0;
    text-shadow: 0 0 20px rgba(26, 115, 232, 0.3);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 8px;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 12px 20px;
    border-radius: var(--border-radius-large);
    transition: all 0.3s var(--transition-standard);
    position: relative;
    display: inline-block;
    letter-spacing: 0.1px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.nav-link:hover {
    background: var(--glass-hover);
    color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
    border-color: rgba(26, 115, 232, 0.3);
}

.nav-link:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.nav-link.active {
    background-color: rgba(138, 180, 248, 0.12);
    color: var(--accent-color);
}

.nav-toggle {
    display: none;
    cursor: pointer;
    color: var(--text-primary);
    padding: 8px;
    border-radius: var(--border-radius-medium);
    transition: all 0.2s var(--transition-standard);
}

.nav-toggle:hover {
    background-color: rgba(232, 234, 237, 0.08);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 2rem;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
}

.hero-title {
    font-family: 'Google Sans', 'Roboto', sans-serif;
    font-size: 64px;
    font-weight: 400;
    margin-bottom: 24px;
    color: var(--text-primary);
    letter-spacing: -1.5px;
    line-height: 1.1;
    text-shadow: 0 0 40px rgba(232, 234, 237, 0.3);
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 22px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0.15px;
    max-width: 600px;
    text-shadow: 0 0 20px rgba(154, 160, 166, 0.3);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Enlarged Transparent Buttons */
.btn {
    padding: 16px 32px;
    border: none;
    border-radius: var(--border-radius-large);
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s var(--transition-standard);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 56px;
    letter-spacing: 0.25px;
    text-transform: none;
    position: relative;
    overflow: hidden;
    outline: none;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity 0.2s var(--transition-standard);
}

.btn:hover::before {
    opacity: 0.08;
}

.btn:focus::before {
    opacity: 0.12;
}

.btn:active::before {
    opacity: 0.16;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: var(--text-white);
    box-shadow: var(--shadow-3), 0 0 20px rgba(26, 115, 232, 0.3);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-hover) 0%, #0f4c8c 100%);
    box-shadow: var(--shadow-4), 0 0 30px rgba(26, 115, 232, 0.5);
    transform: translateY(-4px) scale(1.05);
}

.btn-primary:focus {
    box-shadow: var(--shadow-4), 0 0 0 3px rgba(26, 115, 232, 0.4);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--accent-color);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-2);
}

.btn-secondary:hover {
    background: var(--glass-hover);
    border-color: var(--accent-color);
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-3), 0 0 20px rgba(26, 115, 232, 0.2);
}

.btn-secondary:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.4);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Enlarged Transparent Cards & Images */
.placeholder-image {
    width: 350px;
    height: 350px;
    background: var(--glass-bg);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-glass);
    transition: all 0.3s var(--transition-standard);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.placeholder-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 115, 232, 0.1) 0%, rgba(52, 168, 83, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s var(--transition-standard);
}

.placeholder-image:hover {
    box-shadow: var(--shadow-5), 0 0 40px rgba(26, 115, 232, 0.2);
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(26, 115, 232, 0.3);
}

.placeholder-image:hover::before {
    opacity: 1;
}

.placeholder-image .material-icons {
    font-size: 5rem;
    color: var(--text-secondary);
    z-index: 1;
    position: relative;
    filter: drop-shadow(0 0 10px rgba(26, 115, 232, 0.3));
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-title {
    font-family: 'Google Sans', 'Roboto', sans-serif;
    font-size: 42px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 64px;
    color: var(--text-primary);
    letter-spacing: -0.5px;
    line-height: 1.25;
    text-shadow: 0 0 30px rgba(232, 234, 237, 0.3);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color) 0%, var(--secondary-accent) 100%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(26, 115, 232, 0.5);
}

/* About Section */
.about {
    background-color: var(--secondary-black);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.5;
    letter-spacing: 0.15px;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 32px;
    background: var(--glass-bg);
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-glass);
    transition: all 0.3s var(--transition-standard);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 115, 232, 0.05) 0%, rgba(52, 168, 83, 0.03) 100%);
    opacity: 0;
    transition: opacity 0.3s var(--transition-standard);
}

.stat:hover {
    box-shadow: var(--shadow-4), 0 0 30px rgba(26, 115, 232, 0.2);
    transform: translateY(-6px) scale(1.05);
    border-color: rgba(26, 115, 232, 0.3);
}

.stat:hover::before {
    opacity: 1;
}

.stat h3 {
    font-size: 40px;
    font-weight: 400;
    color: var(--accent-color);
    margin-bottom: 12px;
    font-family: 'Google Sans', 'Roboto', sans-serif;
    text-shadow: 0 0 20px rgba(26, 115, 232, 0.4);
    z-index: 1;
    position: relative;
}

.stat p {
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.25px;
    margin: 0;
    z-index: 1;
    position: relative;
}

.about-image .placeholder-image {
    width: 450px;
    height: 350px;
}

/* Games Section */
.games {
    background-color: var(--primary-black);
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.game-card {
    background: var(--glass-bg);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    border: 1px solid var(--glass-border);
    transition: all 0.3s var(--transition-standard);
    box-shadow: var(--shadow-glass);
    cursor: pointer;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 115, 232, 0.03) 0%, rgba(52, 168, 83, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s var(--transition-standard);
    z-index: 0;
}

.game-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-5), 0 0 40px rgba(26, 115, 232, 0.2);
    border-color: rgba(26, 115, 232, 0.4);
}

.game-card:hover::before {
    opacity: 1;
}

.game-card:focus-within {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.game-image .placeholder-image {
    width: 100%;
    height: 240px;
    border-radius: 0;
    border: none;
    box-shadow: none;
}

.game-info {
    padding: 24px;
    position: relative;
    z-index: 1;
}

.game-info h3 {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--text-primary);
    font-family: 'Google Sans', 'Roboto', sans-serif;
    letter-spacing: 0;
    text-shadow: 0 0 10px rgba(232, 234, 237, 0.2);
}

.game-info p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: 0.25px;
}

.game-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background-color: rgba(138, 180, 248, 0.08);
    color: var(--accent-color);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid rgba(138, 180, 248, 0.2);
    letter-spacing: 0.25px;
    transition: all 0.2s var(--transition-standard);
}

.tag:hover {
    background-color: rgba(138, 180, 248, 0.12);
}

/* News Section */
.news {
    background-color: var(--secondary-black);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.news-card {
    background-color: var(--surface-black);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    border: 1px solid var(--divider);
    transition: all 0.2s var(--transition-standard);
    box-shadow: var(--shadow-1);
    cursor: pointer;
}

.news-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-2);
    border-color: rgba(26, 115, 232, 0.3);
}

.news-card:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.news-card.featured {
    grid-row: span 2;
}

.news-card.featured .news-image .placeholder-image {
    width: 100%;
    height: 250px;
    border-radius: 0;
    border: none;
}

.news-content {
    padding: 20px;
}

.news-date {
    color: var(--accent-color);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.news-content h3 {
    margin: 8px 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 500;
    line-height: 1.3;
    font-family: 'Google Sans', 'Roboto', sans-serif;
}

.news-content p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: 0.25px;
}

.read-more {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s var(--transition-standard);
    letter-spacing: 0.25px;
}

.read-more:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

/* Footer - Material Design */
.footer {
    background-color: var(--secondary-black);
    border-top: 1px solid var(--divider);
    padding: 48px 0 16px 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 16px;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.15px;
}

.footer-section h3 {
    color: var(--accent-color);
    font-family: 'Google Sans', 'Roboto', sans-serif;
    font-size: 18px;
    font-weight: 500;
}

.footer-section p,
.footer-section li {
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: 0.25px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.2s var(--transition-standard);
    padding: 4px 0;
    display: inline-block;
}

.footer-section a:hover {
    color: var(--accent-color);
}

.social-links {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: 1px solid var(--divider);
    border-radius: 50%;
    transition: all 0.2s var(--transition-standard);
    color: var(--text-secondary);
}

.social-link:hover {
    background-color: rgba(26, 115, 232, 0.08);
    border-color: var(--accent-color);
    color: var(--accent-color);
    transform: translateY(-1px);
}

.social-link:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

.footer-bottom {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid var(--divider);
    color: var(--text-disabled);
    font-size: 12px;
    letter-spacing: 0.4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--primary-black);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: block;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .hero-image .placeholder-image,
    .about-image .placeholder-image {
        width: 250px;
        height: 200px;
    }

    .container {
        padding: 0 15px;
    }

    section {
        padding: 3rem 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .nav {
        padding: 1rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-black);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Game Modal Styles */
.game-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: var(--secondary-black);
    padding: 2rem;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid var(--medium-gray);
    animation: slideUp 0.3s ease-out;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    color: var(--text-gray);
    cursor: pointer;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--accent-color);
}

.modal-content h2 {
    color: var(--text-white);
    margin-bottom: 1rem;
    font-family: 'Roboto Slab', serif;
}

.modal-game-image {
    margin: 1rem 0;
    text-align: center;
}

.modal-game-image .placeholder-image {
    width: 100%;
    height: 250px;
    margin: 0 auto;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: center;
}

/* Newsletter Signup */
.newsletter-signup {
    background-color: var(--dark-gray);
    padding: 3rem 0;
    text-align: center;
    border-top: 1px solid var(--medium-gray);
}

.newsletter-signup h3 {
    color: var(--text-white);
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.newsletter-signup p {
    color: var(--text-gray);
    margin-bottom: 2rem;
}

.newsletter-form {
    display: flex;
    justify-content: center;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 6px;
    background-color: var(--secondary-black);
    color: var(--text-white);
    font-size: 1rem;
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--accent-color);
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-container input {
    padding: 8px 40px 8px 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 20px;
    background-color: var(--dark-gray);
    color: var(--text-white);
    width: 200px;
    transition: var(--transition);
}

.search-container input:focus {
    outline: none;
    border-color: var(--accent-color);
    width: 250px;
}

.search-icon {
    position: absolute;
    right: 12px;
    color: var(--text-gray);
    pointer-events: none;
}

/* Contact Section */
.contact {
    background-color: var(--primary-black);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3 {
    color: var(--text-white);
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item .material-icons {
    color: var(--accent-color);
    font-size: 1.5rem;
}

.contact-item p {
    color: var(--text-gray);
    margin: 0;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-form input,
.contact-form textarea {
    padding: 12px 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 6px;
    background-color: var(--secondary-black);
    color: var(--text-white);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--accent-color);
}

.contact-form button {
    align-self: flex-start;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading State */
body:not(.loaded) {
    overflow: hidden;
}

body:not(.loaded) .hero-title,
body:not(.loaded) .hero-subtitle,
body:not(.loaded) .hero-buttons {
    opacity: 0;
    transform: translateY(30px);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        padding: 1.5rem;
        width: 95%;
    }

    .newsletter-form {
        flex-direction: column;
        align-items: center;
    }

    .newsletter-form input {
        width: 100%;
        margin-bottom: 1rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .search-container input {
        width: 150px;
    }

    .search-container input:focus {
        width: 180px;
    }
}

@media (max-width: 480px) {
    .modal-buttons {
        flex-direction: column;
    }

    .modal-buttons .btn {
        width: 100%;
    }
}
