# Studio Black - Game Development Studio Website

A complete, responsive website for a game development studio built with HTML, CSS, and JavaScript featuring a modern black theme with Google Material Design elements.

## Features

### 🎨 Design
- **Black color scheme** with orange accent colors
- **Google Fonts** integration (Roboto & Roboto Slab)
- **Material Icons** for enhanced visual elements
- **Responsive design** for all device sizes
- **Smooth animations** and transitions

### 📱 Sections
1. **Header** - Fixed navigation with mobile menu
2. **Hero/Main** - Studio introduction with call-to-action buttons
3. **About Us** - Studio information with animated statistics
4. **Games** - Interactive game showcase with modal popups
5. **News** - Latest updates and announcements
6. **Footer** - Contact information and social links

### ⚡ Interactive Features
- **Mobile-responsive navigation** with hamburger menu
- **Smooth scrolling** between sections
- **Game card modals** with detailed information
- **Animated statistics** counter
- **Parallax effects** on scroll
- **Hover animations** throughout
- **Social media integration** placeholders

### 🛠 Technical Features
- **Vanilla HTML/CSS/JavaScript** (no frameworks)
- **CSS Grid & Flexbox** for modern layouts
- **Intersection Observer API** for scroll animations
- **CSS Custom Properties** for consistent theming
- **Mobile-first responsive design**
- **Accessibility considerations**

## File Structure

```
studio-website/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── script.js           # Interactive JavaScript
└── README.md           # Documentation
```

## Getting Started

1. **Clone or download** the files to your local machine
2. **Open `index.html`** in your web browser
3. **No build process required** - it's ready to use!

## Customization

### Colors
Edit the CSS custom properties in `styles.css`:
```css
:root {
    --primary-black: #000000;
    --accent-color: #ff6b35;
    /* ... other colors */
}
```

### Content
- Update text content in `index.html`
- Replace placeholder images with actual studio images
- Modify game information in the Games section
- Update contact information in the Footer

### Features
Uncomment these lines in `script.js` to enable additional features:
```javascript
// addNewsletterSignup();
// addSearchFeature();
```

## Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## Performance

- **Lightweight** - No external frameworks
- **Fast loading** - Optimized CSS and JavaScript
- **Smooth animations** - Hardware-accelerated transitions
- **Responsive images** - Placeholder system ready for real images

## Future Enhancements

- Add real game images and content
- Implement backend for contact form
- Add blog/news management system
- Integrate with social media APIs
- Add game trailers and videos
- Implement search functionality
- Add multi-language support

## License

This project is open source and available under the [MIT License](LICENSE).

---

**Studio Black** - Crafting immersive gaming experiences in the shadows 🎮
